package com.gnico.majo.application.port.`in`

import com.gnico.majo.adapter.controller.dto.SaleItemRequest
import com.gnico.majo.application.domain.model.ExternalSaleDetail
import com.gnico.majo.application.domain.model.Id

interface SaleService {

    /**
     * Crea una nueva venta y sus ítems.
     * @return ID de la venta creada
     */
    suspend fun createSale(clienteId: Int?, vendedorId: Int, itemsRequest: List<SaleItemRequest>) : Id

    /**
     * Crea un comprobante para una venta y lo imprime.
     * @return ID del comprobante creado
     */
    fun createComprobante(ventaId: Id, tipoComprobante: String, puntoVenta: Int) : Id

    /**
     * Obtiene los detalles de una venta desde la base de datos externa
     * @param codigo Número de documento de la venta (código de 8 dígitos)
     * @return Detalles de la venta o null si no se encuentra
     */
    suspend fun getExternalSaleDetails(codigo: String): ExternalSaleDetail?

}