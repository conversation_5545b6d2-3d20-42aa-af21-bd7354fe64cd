package com.gnico.majo.application.usecase

import com.gnico.majo.application.domain.model.Usuario
import com.gnico.majo.application.port.`in`.UsuarioService
import com.gnico.majo.application.port.out.UsuarioRepository
import com.gnico.majo.application.port.out.ExternalUserRepositoryPort
import com.gnico.majo.application.port.out.ExternalUser

class UsuarioServiceImpl(
    private val usuarioRepository: UsuarioRepository,
    private val externalUserRepository: ExternalUserRepositoryPort
) : UsuarioService {

    override suspend fun getAllActiveUsers(): List<Usuario> {
        return usuarioRepository.findAllActive()
    }

    override suspend fun getAllUsers(): List<Usuario> {
        return usuarioRepository.findAll()
    }

    override suspend fun getUserByUsername(username: String): Usuario? {
        require(username.isNotBlank()) { "Username no puede estar vacío" }
        return usuarioRepository.findByUsername(username)
    }

    override suspend fun createUser(
        username: String,
        nombre: String,
        nombreDisplay: String,
        activo: Boolean
    ): String {
        // Validar que el usuario no exista
        val existingUser = usuarioRepository.findByUsername(username)
        if (existingUser != null) {
            throw IllegalArgumentException("Ya existe un usuario con username '$username'")
        }

        val usuario = Usuario.create(
            username = username,
            nombre = nombre,
            nombreDisplay = nombreDisplay,
            activo = activo
        )

        return usuarioRepository.save(usuario)
    }

    override suspend fun updateUser(usuario: Usuario): Boolean {
        // Verificar que el usuario existe
        val existingUser = usuarioRepository.findByUsername(usuario.username)
            ?: throw IllegalArgumentException("Usuario '${usuario.username}' no encontrado")

        return usuarioRepository.update(usuario)
    }

    override suspend fun deleteUser(username: String): Boolean {
        require(username.isNotBlank()) { "Username no puede estar vacío" }

        // Verificar que el usuario existe
        val existingUser = usuarioRepository.findByUsername(username)
            ?: throw IllegalArgumentException("Usuario '$username' no encontrado")

        return usuarioRepository.delete(username)
    }

    override suspend fun syncUsers(): Int {
        // 1. Limpiar la tabla interna de usuarios
        usuarioRepository.deleteAll()

        // 2. Obtener todos los usuarios externos
        val externalUsers = externalUserRepository.findAllUsers()

        // 3. Mapear usuarios externos a usuarios internos
        val usuariosInternos = externalUsers.map { externalUser ->
            mapExternalToInternal(externalUser)
        }

        // 4. Insertar usuarios en la tabla interna
        val syncedCount = usuarioRepository.saveAll(usuariosInternos)

        return syncedCount
    }

    /**
     * Mapea un usuario externo a un usuario interno
     */
    private fun mapExternalToInternal(externalUser: ExternalUser): Usuario {
        return Usuario.create(
            username = externalUser.username,
            nombre = externalUser.name,
            nombreDisplay = externalUser.name,
            activo = true
        )
    }
}
