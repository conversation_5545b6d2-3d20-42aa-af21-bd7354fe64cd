package com.gnico.majo.application.usecase

import com.gnico.majo.adapter.controller.dto.SaleItemRequest
import com.gnico.majo.application.port.`in`.SaleService
import com.gnico.majo.application.domain.model.Cliente
import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.ExternalSaleDetail
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.SaleItem
import com.gnico.majo.application.domain.model.Usuario
import com.gnico.majo.application.port.out.PrinterPort
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.application.port.out.SalesSummary
import com.gnico.majo.application.port.out.ClienteRepository
import com.gnico.majo.application.port.out.ExternalSaleRepositoryPort
import com.gnico.majo.application.port.out.TipoIvaRepository
import com.gnico.majo.application.port.out.UsuarioRepository
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class SaleServiceImpl(
    private val saleRepository: SaleRepositoryPort,
    private val printer: PrinterPort,
    private val salesReport: SalesReportPort,
    private val usuarioRepository: UsuarioRepository,
    private val clienteRepository: ClienteRepository,
    private val tipoIvaRepository: TipoIvaRepository,
    private val externalSaleRepository: ExternalSaleRepositoryPort
) : SaleService {

    override suspend fun createSale(
        clienteId: Int?,
        vendedorId: Int,
        itemsRequest: List<SaleItemRequest>
    ): Id {
        // Obtener usuario por username - convertimos vendedorId a string para buscar por username
        val usuario = usuarioRepository.findByUsername(vendedorId.toString())
            ?: throw IllegalArgumentException("Usuario con username '$vendedorId' no encontrado")

        // Obtener cliente (opcional)
        val cliente = clienteId?.let { id ->
            clienteRepository.findById(Id(id))
                ?: throw IllegalArgumentException("Cliente $id no encontrado")
        }

        // Mapear ítems
        val items = itemsRequest.map { item ->
            SaleItem(
                productoCodigo = item.productoId,
                cantidad = BigDecimal.valueOf(item.cantidad),
                precioUnitario = BigDecimal.valueOf(item.precioUnitario),
                tipoIva = Id(item.tipoIvaId),
                subtotal = BigDecimal.ZERO, // Calculado más adelante
                baseImp = BigDecimal.ZERO,  // Calculado más adelante
                importeIva = BigDecimal.ZERO // Calculado más adelante
            )
        }

        // Obtener tipos de IVA
        val tiposIva = tipoIvaRepository.findAll().associate { it.id to it.porcentaje }

        return createSale(cliente, usuario, items, tiposIva)
    }

    // Metodo original renombrado a private
    private fun createSale(
        cliente: Cliente?,
        usuario: Usuario, // Cambio de vendedor a usuario
        items: List<SaleItem>,
        tiposIva: Map<Id, BigDecimal>
    ): Id {
        val numeroVenta = "V-${UUID.randomUUID().toString().substring(0, 8)}"
        var montoTotal = BigDecimal.ZERO

        // Calcular subtotales e IVA para los ítems
        val validatedItems = items.map { item ->
            val porcentajeIva = tiposIva[item.tipoIva]
                ?: throw IllegalArgumentException("Tipo IVA ${item.tipoIva.value} no encontrado")
            val subtotal = item.cantidad.multiply(item.precioUnitario)
            val baseImp = subtotal
            val importeIva = baseImp.multiply(porcentajeIva.divide(BigDecimal("100")))
            montoTotal += subtotal.add(importeIva)

            item.copy(subtotal = subtotal, baseImp = baseImp, importeIva = importeIva)
        }

        val sale = Sale(
            numeroVenta = numeroVenta,
            cliente = cliente,
            usuario = usuario, // Cambio de vendedor a usuario
            fechaVenta = LocalDateTime.now(),
            montoTotal = montoTotal,
            estado = "COMPLETADA",
            items = validatedItems
        )

        return saleRepository.saveSale(sale)
    }


    override fun createComprobante(
        ventaId: Id,
        tipoComprobante: String,
        puntoVenta: Int
    ): Id {
        val sale = saleRepository.findSaleById(ventaId)
            ?: throw IllegalArgumentException("Venta ${ventaId.value} no encontrada")

        if (tipoComprobante == "FACTURA_A" && sale.cliente == null) {
            throw IllegalArgumentException("Factura A requiere un cliente")
        }

        // Calcular importes desde los ítems
        val impNeto = sale.items.sumOf { it.baseImp }
        val impIva = sale.items.sumOf { it.importeIva }
        val impTotal = impNeto.add(impIva)
        val impTotConc = BigDecimal.ZERO
        val impTrib = BigDecimal.ZERO

        val comprobante = Comprobante(
            venta = ventaId,
            tipoComprobante = tipoComprobante,
            puntoVenta = puntoVenta,
            numeroComprobante = 0, // Será asignado por el adaptador (o WSFE)
            cae = "PENDING", // Será actualizado tras WSFE
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = impTotal,
            impTotConc = impTotConc,
            impNeto = impNeto,
            impIva = impIva,
            impTrib = impTrib,
            monId = "PES",
            monCotiz = BigDecimal("1.0"),
            estado = "PENDIENTE"
        )

        val comprobanteId = saleRepository.saveComprobante(comprobante)
        val savedComprobante = saleRepository.findComprobanteById(comprobanteId)
            ?: throw IllegalStateException("Comprobante ${comprobanteId.value} no encontrado")

        // Imprimir comprobante
        printer.printComprobante(savedComprobante, sale)

        return comprobanteId
    }

    /**
     * Obtiene un resumen de ventas con/sin comprobante.
     */
    fun getSalesSummary(startDate: LocalDateTime, endDate: LocalDateTime): SalesSummary {
        return salesReport.getSalesSummary(startDate, endDate)
    }

    override suspend fun getExternalSaleDetails(codigo: String): ExternalSaleDetail? {
        require(codigo.isNotBlank()) { "El código no puede estar vacío" }
        return externalSaleRepository.findSaleDetailsByDocumentNo(codigo)
    }
}