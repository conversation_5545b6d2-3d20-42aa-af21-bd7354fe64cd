package com.gnico.majo.application.domain.model

import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

data class Id(val value: Int) // Identificador genérico para el dominio

data class Cliente(val id: Id, val nombre: String, val cuit: String?)

data class Sale(
    val id: Id? = null,
    val numeroVenta: String,
    val cliente: Cliente?,
    val usuario: Usuario,
    val fechaVenta: LocalDateTime,
    val montoTotal: BigDecimal,
    val estado: String,
    val items: List<SaleItem>
)

data class SaleItem(
    val productoCodigo: Int, // Ahora usa el código del producto
    val cantidad: BigDecimal,
    val precioUnitario: BigDecimal,
    val tipoIva: Id,
    val subtotal: BigDecimal,
    val baseImp: BigDecimal,
    val importeIva: BigDecimal
)

data class Comprobante(
    val id: Id? = null,
    val venta: Id,
    val tipoComprobante: String,
    val puntoVenta: Int,
    val numeroComprobante: Int,
    val cae: String,
    val fechaEmision: LocalDateTime,
    val fechaVencimientoCae: LocalDate,
    val impTotal: BigDecimal,
    val impTotConc: BigDecimal,
    val impNeto: BigDecimal,
    val impIva: BigDecimal,
    val impTrib: BigDecimal,
    val monId: String,
    val monCotiz: BigDecimal,
    val estado: String
)