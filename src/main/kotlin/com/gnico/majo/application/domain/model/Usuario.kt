package com.gnico.majo.application.domain.model

import kotlinx.serialization.Serializable
import java.time.LocalDateTime

/**
 * Modelo de dominio para Usuario
 */
@Serializable
data class Usuario(
    val username: String,
    val nombre: String,
    val nombreDisplay: String,
    val activo: Boolean = true,
    val creadoEn: LocalDateTime? = null,
    val actualizadoEn: LocalDateTime? = null
) {
    companion object {
        fun create(
            username: String,
            nombre: String,
            nombreDisplay: String,
            activo: Boolean = true
        ): Usuario {
            require(username.isNotBlank()) { "Username no puede estar vacío" }
            require(username.length <= 50) { "Username no puede exceder 50 caracteres" }
            require(nombre.isNotBlank()) { "Nombre no puede estar vacío" }
            require(nombre.length <= 100) { "Nombre no puede exceder 100 caracteres" }
            require(nombreDisplay.isNotBlank()) { "Nombre display no puede estar vacío" }
            require(nombreDisplay.length <= 100) { "Nombre display no puede exceder 100 caracteres" }

            return Usuario(
                username = username.trim(),
                nombre = nombre.trim(),
                nombreDisplay = nombreDisplay.trim(),
                activo = activo
            )
        }
    }
}


