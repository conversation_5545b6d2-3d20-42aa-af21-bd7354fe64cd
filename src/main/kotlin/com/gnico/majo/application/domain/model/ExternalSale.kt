package com.gnico.majo.application.domain.model

import java.math.BigDecimal
import java.time.LocalDateTime

data class ExternalSaleDetail(
    val invoiceId: Int,
    val documentNo: String,
    val datePrinted: LocalDateTime?,
    val totalLines: BigDecimal,
    val grandTotal: BigDecimal,
    val createdBy: Int,
    val lines: List<ExternalSaleLineDetail>
)

data class ExternalSaleLineDetail(
    val line: Int,
    val productId: Int,
    val productName: String,
    val qtyInvoiced: BigDecimal,
    val priceActual: BigDecimal,
    val lineNetAmt: BigDecimal,
    val uomId: Int
)
