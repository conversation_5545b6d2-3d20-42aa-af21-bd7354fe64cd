package com.gnico.majo.infrastructure.routes

import com.gnico.majo.adapter.controller.rest.SaleController
import com.gnico.majo.adapter.controller.dto.ErrorResponse
import com.gnico.majo.adapter.controller.dto.SaleRequest
import com.gnico.majo.adapter.controller.dto.SaleResponse
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.routing

fun Application.configureSaleRoutes(saleController: SaleController) {
    routing {
        post("/sales") {
            try {
                val request = call.receive<SaleRequest>()
                val saleId = saleController.createSale(request)
                call.respond(HttpStatusCode.Created, SaleResponse(saleId.value))
            } catch (e: IllegalArgumentException) {
                call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Invalid request"))
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
            }
        }
    }
}
