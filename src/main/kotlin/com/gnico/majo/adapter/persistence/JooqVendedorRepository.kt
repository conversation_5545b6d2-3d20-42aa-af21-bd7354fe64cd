package com.gnico.majo.adapter.persistence

import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Usuario
import com.gnico.majo.application.domain.model.Vendedor
import com.gnico.majo.application.port.out.VendedorRepository
import com.gnico.majo.application.port.out.UsuarioRepository
import com.gnico.majo.jooq.generated.tables.Usuarios.Companion.USUARIOS
import org.jooq.impl.DSL
import java.time.LocalDateTime

class JooqUsuarioRepository : UsuarioRepository {

    override suspend fun findByUsername(username: String): Usua<PERSON>? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(USUARIOS)
                .where(USUARIOS.USERNAME.eq(username))
                .fetchOne()
                ?.let { record ->
                    Usuario(
                        username = record.username!!,
                        nombre = record.nombre!!,
                        nombreDisplay = record.nombreDisplay!!,
                        activo = record.activo ?: true,
                        creadoEn = record.creadoEn,
                        actualizadoEn = record.actualizadoEn
                    )
                }
        }
    }

    override suspend fun findAllActive(): List<Usuario> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(USUARIOS)
                .where(USUARIOS.ACTIVO.eq(true))
                .orderBy(USUARIOS.NOMBRE)
                .fetch()
                .map { record ->
                    Usuario(
                        username = record.username!!,
                        nombre = record.nombre!!,
                        nombreDisplay = record.nombreDisplay!!,
                        activo = record.activo ?: true,
                        creadoEn = record.creadoEn,
                        actualizadoEn = record.actualizadoEn
                    )
                }
        }
    }

    override suspend fun findAll(): List<Usuario> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(USUARIOS)
                .orderBy(USUARIOS.NOMBRE)
                .fetch()
                .map { record ->
                    Usuario(
                        username = record.username!!,
                        nombre = record.nombre!!,
                        nombreDisplay = record.nombreDisplay!!,
                        activo = record.activo ?: true,
                        creadoEn = record.creadoEn,
                        actualizadoEn = record.actualizadoEn
                    )
                }
        }
    }

    override suspend fun save(usuario: Usuario): String {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val record = dsl.newRecord(USUARIOS).apply {
                username = usuario.username
                nombre = usuario.nombre
                nombreDisplay = usuario.nombreDisplay
                activo = usuario.activo
                creadoEn = LocalDateTime.now()
                actualizadoEn = LocalDateTime.now()
            }
            record.store()
            usuario.username
        }
    }

    override suspend fun update(usuario: Usuario): Boolean {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val count = dsl.update(USUARIOS)
                .set(USUARIOS.NOMBRE, usuario.nombre)
                .set(USUARIOS.NOMBRE_DISPLAY, usuario.nombreDisplay)
                .set(USUARIOS.ACTIVO, usuario.activo)
                .set(USUARIOS.ACTUALIZADO_EN, LocalDateTime.now())
                .where(USUARIOS.USERNAME.eq(usuario.username))
                .execute()

            count > 0
        }
    }

    override suspend fun delete(username: String): Boolean {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val count = dsl.update(USUARIOS)
                .set(USUARIOS.ACTIVO, false)
                .set(USUARIOS.ACTUALIZADO_EN, LocalDateTime.now())
                .where(USUARIOS.USERNAME.eq(username))
                .execute()

            count > 0
        }
    }
}

// Clase de compatibilidad para mantener la interfaz VendedorRepository
class JooqVendedorRepository : VendedorRepository {
    private val usuarioRepository = JooqUsuarioRepository()

    override suspend fun findById(id: Id): Vendedor? {
        // Para mantener compatibilidad, buscaremos por username usando el valor del id
        // Esto es temporal hasta que se actualicen todas las referencias
        return usuarioRepository.findByUsername(id.value.toString())
    }
}