package com.gnico.majo.adapter.persistence

import com.gnico.majo.application.domain.model.ExternalSaleDetail
import com.gnico.majo.application.domain.model.ExternalSaleLineDetail
import com.gnico.majo.application.port.out.ExternalSaleRepositoryPort
import com.gnico.majo.infrastructure.config.ExternalDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.sql.SQLException
import java.sql.Timestamp
import java.time.LocalDateTime

class JdbcExternalSaleRepository : ExternalSaleRepositoryPort {

    override suspend fun findSaleDetailsByDocumentNo(documentNo: String): ExternalSaleDetail? = withContext(Dispatchers.IO) {
        println("DEBUG: Buscando venta con documentNo: $documentNo")
        try {
            ExternalDatabase.getConnection().use { connection ->
                val sql = """
                    select 
                    i.invoice_id,
                    i.documentno,
                    i.dateprinted,
                    i.totallines,
                    i.grandtotal,
                    i.createdby,
                    l.line,
                    l.product_id, 
                    p.name,
                    l.qtyinvoiced,
                    l.priceactual,
                    l.linenetamt,
                    l.uom_id
                    from invoice i 
                    join invoiceline l
                    on i.invoice_id = l.invoice_id 
                    join product p 
                    on l.product_id = p.product_id
                    where i.documentno = ?
                    order by l.line
                """.trimIndent()

                connection.prepareStatement(sql).use { statement ->
                    statement.setString(1, documentNo)
                    statement.executeQuery().use { resultSet ->
                        
                        var saleHeader: ExternalSaleDetail? = null
                        val lines = mutableListOf<ExternalSaleLineDetail>()
                        
                        while (resultSet.next()) {
                            // Si es la primera fila, crear el header de la venta
                            if (saleHeader == null) {
                                val datePrintedTimestamp = resultSet.getTimestamp("dateprinted")
                                val datePrinted = datePrintedTimestamp?.toLocalDateTime()
                                
                                saleHeader = ExternalSaleDetail(
                                    invoiceId = resultSet.getInt("invoice_id"),
                                    documentNo = resultSet.getString("documentno"),
                                    datePrinted = datePrinted,
                                    totalLines = resultSet.getBigDecimal("totallines") ?: BigDecimal.ZERO,
                                    grandTotal = resultSet.getBigDecimal("grandtotal") ?: BigDecimal.ZERO,
                                    createdBy = resultSet.getInt("createdby"),
                                    lines = emptyList() // Se asignará después
                                )
                            }
                            
                            // Agregar línea de detalle
                            val lineDetail = ExternalSaleLineDetail(
                                line = resultSet.getInt("line"),
                                productId = resultSet.getInt("product_id"),
                                productName = resultSet.getString("name") ?: "",
                                qtyInvoiced = resultSet.getBigDecimal("qtyinvoiced") ?: BigDecimal.ZERO,
                                priceActual = resultSet.getBigDecimal("priceactual") ?: BigDecimal.ZERO,
                                lineNetAmt = resultSet.getBigDecimal("linenetamt") ?: BigDecimal.ZERO,
                                uomId = resultSet.getInt("uom_id")
                            )
                            lines.add(lineDetail)
                        }
                        
                        // Retornar el header con las líneas asignadas
                        return@withContext saleHeader?.copy(lines = lines)
                    }
                }
            }
        } catch (e: SQLException) {
            throw RuntimeException("Error al obtener detalles de venta $documentNo de la base de datos externa", e)
        }
    }
}
