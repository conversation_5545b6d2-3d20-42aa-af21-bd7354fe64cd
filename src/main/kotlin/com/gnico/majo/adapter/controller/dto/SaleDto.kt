package com.gnico.majo.adapter.controller.dto

import kotlinx.serialization.Serializable

@Serializable
data class SaleRequest(
    val vendedorId: Int,
    val clienteId: Int?,
    val items: List<SaleItemRequest>
)

@Serializable
data class SaleItemRequest(
    val productoId: Int,
    val cantidad: Double,
    val precioUnitario: Double,
    val tipoIvaId: Int
)

@Serializable
data class SaleResponse(val saleId: Int)

@Serializable
data class ErrorResponse(val message: String)

@Serializable
data class SaleDetailResponse(
    val invoiceId: Int,
    val documentNo: String,
    val datePrinted: String?,
    val totalLines: Double,
    val grandTotal: Double,
    val createdBy: Int,
    val lines: List<SaleLineDetailResponse>
)

@Serializable
data class SaleLineDetailResponse(
    val line: Int,
    val productId: Int,
    val productName: String,
    val qtyInvoiced: Double,
    val priceActual: Double,
    val lineNetAmt: Double,
    val uomId: Int
)
