package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.adapter.controller.dto.SaleDetailResponse
import com.gnico.majo.adapter.controller.dto.SaleLineDetailResponse
import com.gnico.majo.adapter.controller.dto.SaleRequest
import com.gnico.majo.application.domain.model.ExternalSaleDetail
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.`in`.SaleService
import java.time.format.DateTimeFormatter

class SaleController(
    private val saleService: SaleService
) {
    suspend fun createSale(request: SaleRequest): Id {
        return saleService.createSale(
            clienteId = request.clienteId,
            vendedorId = request.vendedorId,
            itemsRequest = request.items
        )
    }
}