package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.adapter.controller.rest.dto.ErrorResponse
import com.gnico.majo.adapter.controller.rest.dto.SyncResponse
import com.gnico.majo.application.port.`in`.UsuarioService
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable

@Serializable
data class CreateUserRequest(
    val username: String,
    val nombre: String,
    val nombreDisplay: String,
    val activo: Boolean = true
)

@Serializable
data class UpdateUserRequest(
    val nombre: String,
    val nombreDisplay: String,
    val activo: Boolean
)

@Serializable
data class UserResponse(
    val username: String,
    val message: String
)

@Serializable
data class UsuarioDto(
    val username: String,
    val nombre: String,
    val nombreDisplay: String,
    val activo: Boolean
)

class UsuarioController(
    private val usuarioService: UsuarioService
) {

    suspend fun getAllUsers(call: RoutingCall) {
        try {
            val users = usuarioService.getAllUsers()
            val usersDto = users.map { mapToDto(it) }
            call.respond(HttpStatusCode.OK, usersDto)
        } catch (e: Exception) {
            call.respond(
                HttpStatusCode.InternalServerError,
                ErrorResponse("Error al obtener usuarios: ${e.message}")
            )
        }
    }

    suspend fun getAllActiveUsers(call: RoutingCall) {
        try {
            val users = usuarioService.getAllActiveUsers()
            val usersDto = users.map { mapToDto(it) }
            call.respond(HttpStatusCode.OK, usersDto)
        } catch (e: Exception) {
            call.respond(
                HttpStatusCode.InternalServerError,
                ErrorResponse("Error al obtener usuarios activos: ${e.message}")
            )
        }
    }

    suspend fun getUserByUsername(call: RoutingCall) {
        try {
            val username = call.parameters["username"]
            if (username.isNullOrBlank()) {
                call.respond(
                    HttpStatusCode.BadRequest,
                    ErrorResponse("Username es requerido")
                )
                return
            }

            val user = usuarioService.getUserByUsername(username)
            if (user != null) {
                call.respond(HttpStatusCode.OK, mapToDto(user))
            } else {
                call.respond(
                    HttpStatusCode.NotFound,
                    ErrorResponse("Usuario no encontrado")
                )
            }
        } catch (e: Exception) {
            call.respond(
                HttpStatusCode.InternalServerError,
                ErrorResponse("Error al obtener usuario: ${e.message}")
            )
        }
    }

    suspend fun createUser(call: RoutingCall) {
        try {
            val request = call.receive<CreateUserRequest>()
            
            val username = usuarioService.createUser(
                username = request.username,
                nombre = request.nombre,
                nombreDisplay = request.nombreDisplay,
                activo = request.activo
            )
            
            call.respond(
                HttpStatusCode.Created,
                UserResponse(username, "Usuario creado exitosamente")
            )
        } catch (e: IllegalArgumentException) {
            call.respond(
                HttpStatusCode.BadRequest,
                ErrorResponse(e.message ?: "Datos inválidos")
            )
        } catch (e: Exception) {
            call.respond(
                HttpStatusCode.InternalServerError,
                ErrorResponse("Error al crear usuario: ${e.message}")
            )
        }
    }

    suspend fun updateUser(call: RoutingCall) {
        try {
            val username = call.parameters["username"]
            if (username.isNullOrBlank()) {
                call.respond(
                    HttpStatusCode.BadRequest,
                    ErrorResponse("Username es requerido")
                )
                return
            }

            val request = call.receive<UpdateUserRequest>()
            val usuario = com.gnico.majo.application.domain.model.Usuario.create(
                username = username,
                nombre = request.nombre,
                nombreDisplay = request.nombreDisplay,
                activo = request.activo
            )

            val updated = usuarioService.updateUser(usuario)
            if (updated) {
                call.respond(
                    HttpStatusCode.OK,
                    UserResponse(username, "Usuario actualizado exitosamente")
                )
            } else {
                call.respond(
                    HttpStatusCode.NotFound,
                    ErrorResponse("Usuario no encontrado")
                )
            }
        } catch (e: IllegalArgumentException) {
            call.respond(
                HttpStatusCode.BadRequest,
                ErrorResponse(e.message ?: "Datos inválidos")
            )
        } catch (e: Exception) {
            call.respond(
                HttpStatusCode.InternalServerError,
                ErrorResponse("Error al actualizar usuario: ${e.message}")
            )
        }
    }

    suspend fun deleteUser(call: RoutingCall) {
        try {
            val username = call.parameters["username"]
            if (username.isNullOrBlank()) {
                call.respond(
                    HttpStatusCode.BadRequest,
                    ErrorResponse("Username es requerido")
                )
                return
            }

            val deleted = usuarioService.deleteUser(username)
            if (deleted) {
                call.respond(
                    HttpStatusCode.OK,
                    UserResponse(username, "Usuario eliminado exitosamente")
                )
            } else {
                call.respond(
                    HttpStatusCode.NotFound,
                    ErrorResponse("Usuario no encontrado")
                )
            }
        } catch (e: IllegalArgumentException) {
            call.respond(
                HttpStatusCode.BadRequest,
                ErrorResponse(e.message ?: "Datos inválidos")
            )
        } catch (e: Exception) {
            call.respond(
                HttpStatusCode.InternalServerError,
                ErrorResponse("Error al eliminar usuario: ${e.message}")
            )
        }
    }

    suspend fun syncUsers(call: RoutingCall) {
        try {
            val syncedCount = usuarioService.syncUsers()
            call.respond(
                HttpStatusCode.OK,
                SyncResponse("Sincronización de usuarios completada", syncedCount)
            )
        } catch (e: Exception) {
            call.respond(
                HttpStatusCode.InternalServerError,
                ErrorResponse("Error al sincronizar usuarios: ${e.message}")
            )
        }
    }

    private fun mapToDto(usuario: com.gnico.majo.application.domain.model.Usuario): UsuarioDto {
        return UsuarioDto(
            username = usuario.username,
            nombre = usuario.nombre,
            nombreDisplay = usuario.nombreDisplay,
            activo = usuario.activo
        )
    }
}
